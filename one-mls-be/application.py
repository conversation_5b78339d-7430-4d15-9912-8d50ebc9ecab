from flask import Flask, request, jsonify, Response, stream_template
from flask_cors import CORS
from dotenv import load_dotenv
from pymongo import MongoClient
from bson import ObjectId
import os
import uuid
from datetime import datetime, timedelta
from werkzeug.utils import secure_filename
from pymongo import MongoClient
from bson import ObjectId
import bcrypt
from bson.binary import Binary
import jwt
import re
import json
import ssl
from bson.json_util import dumps
from src.libraries.storage import storage_service
from src.libraries.database import DatabaseConnection
from src.routes.saved_listings_routes import init_saved_listings_routes
from src.routes.auth_routes import init_auth_routes
import google.generativeai as genai
import time

# Custom JSON encoder to handle MongoDB types
class MongoJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, ObjectId):
            return str(obj)
        if isinstance(obj, datetime):
            return obj.isoformat()
        if isinstance(obj, bytes):
            return str(obj)
        # Add handling for other MongoDB specific types
        try:
            return json.JSONEncoder.default(self, obj)
        except TypeError:
            return str(obj)

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Initialize Gemini AI
gemini_api_key = os.getenv('GEMINI_API_KEY')
if gemini_api_key:
    genai.configure(api_key=gemini_api_key)
    print("Gemini AI configured successfully")
else:
    print("WARNING: GEMINI_API_KEY not found. Chat will use fallback responses.")

app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'your-secret-key-change-in-production')
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'pdf', 'mp4', 'mov', 'avi', 'webm'}
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

# Initialize MongoDB connection
mongodb_uri = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/one_mls_db')
try:
    client = MongoClient(
        mongodb_uri, 
        serverSelectionTimeoutMS=10000,
        tls=True,
        tlsAllowInvalidCertificates=True
    )
    # Extract database name from URI or use default
    if 'one_mls_db' in mongodb_uri:
        db = client.one_mls_db
    else:
        db = client.one_mls_db  # Use default database name
    users_collection = db.users
    properties_collection = db.properties
    verifications_collection = db.verifications
    listings_collection = db.listings
    messages_collection = db.messages
    
    client.admin.command('ping')
    print("MongoDB connection successful")
    
    try:
        users_collection.create_index("email", unique=True)
        verifications_collection.create_index("email")
        verifications_collection.create_index("id")
        listings_collection.create_index("id")
        listings_collection.create_index([("city", 1), ("state", 1)])
        listings_collection.create_index("propertyType")
        messages_collection.create_index("listingId")
        messages_collection.create_index("createdAt")
        print("MongoDB indexes created successfully")
    except Exception as e:
        print(f"Warning: Could not create indexes: {e}")
        
except Exception as e:
    print(f"MongoDB connection failed: {e}")
    print("Server will start with fallback mock data")
    client = None
    db = None
    users_collection = None
    properties_collection = None
    verifications_collection = None
    listings_collection = None
    messages_collection = None

# Initialize database connection for blueprints
db_connection = DatabaseConnection()

# Register blueprints
auth_bp = init_auth_routes(db_connection)
saved_listings_bp = init_saved_listings_routes(db_connection)

app.register_blueprint(auth_bp)
app.register_blueprint(saved_listings_bp)

def validate_email(email):
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def generate_jwt_token(user_id, email):
    payload = {
        'user_id': str(user_id),
        'email': email,
        'exp': datetime.utcnow() + timedelta(hours=24)
    }
    return jwt.encode(payload, app.config['JWT_SECRET_KEY'], algorithm='HS256')

def verify_jwt_token(token):
    try:
        payload = jwt.decode(token, app.config['JWT_SECRET_KEY'], algorithms=['HS256'])
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

def require_auth(f):
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'message': 'No token provided'}), 401
        
        if token.startswith('Bearer '):
            token = token[7:]
        
        payload = verify_jwt_token(token)
        if not payload:
            return jsonify({'message': 'Invalid or expired token'}), 401
        
        request.current_user_id = payload['user_id']
        request.current_user_email = payload['email']
        return f(*args, **kwargs)
    return decorated_function

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def save_file(file, prefix='', user_id=None):
    if file and allowed_file(file.filename):
        try:
            if prefix == 'profile' and user_id:
                return storage_service.upload_profile_picture(file, user_id)
            elif prefix.startswith('listing_video_'):
                return storage_service.upload_video(file, prefix)
            else:
                filename = secure_filename(file.filename)
                unique_filename = f"{prefix}_{uuid.uuid4().hex}_{filename}"
                return storage_service.upload_profile_picture(file, unique_filename)
        except Exception as e:
            print(f"File upload error: {e}")
            return None
    return None

@app.route('/')
def hello_world():
    return {'message': 'OneMLS Backend API'}

@app.route('/test')
def test_mongodb():
    try:
        if client is None:
            return {
                'status': 'error',
                'message': 'MongoDB connection not initialized'
            }, 500
        
        # Check connection
        client.admin.command('ping')
        
        # Use bson.json_util to safely serialize MongoDB data
        response_data = {}
        response_data['status'] = 'success'
        response_data['message'] = 'MongoDB connection successful'
        response_data['database'] = db.name
        
        # Get collection information
        collections_info = {}
        for collection_name in db.list_collection_names():
            collection = db[collection_name]
            count = collection.count_documents({})
            # Only include count which is safely serializable
            collections_info[collection_name] = {
                'count': count,
                'indexes': [idx['name'] for idx in collection.list_indexes()]
            }
        
        response_data['collections'] = collections_info
        
        # Server status with limited info that's JSON serializable
        server_status = client.admin.command('serverStatus')
        response_data['server'] = {
            'version': server_status.get('version', ''),
            'uptime_seconds': server_status.get('uptime', 0),
            'connections': server_status.get('connections', {}).get('current', 0)
        }
        
        # Return response using the custom encoder
        return json.loads(dumps(response_data)), 200
    except Exception as e:
        return {
            'status': 'error',
            'message': f'MongoDB connection failed: {str(e)}'
        }, 500

def filter_properties(properties, min_price, max_price, bedrooms, bathrooms, property_types, min_sqft, max_sqft):
    filtered = properties
    
    if min_price:
        filtered = [p for p in filtered if p['price'] >= min_price]
    if max_price:
        filtered = [p for p in filtered if p['price'] <= max_price]
    if bedrooms:
        filtered = [p for p in filtered if p['bedrooms'] >= bedrooms]
    if bathrooms:
        filtered = [p for p in filtered if p['bathrooms'] >= bathrooms]
    if property_types:
        filtered = [p for p in filtered if p['propertyType'] in property_types]
    if min_sqft:
        filtered = [p for p in filtered if p['squareFootage'] >= min_sqft]
    if max_sqft:
        filtered = [p for p in filtered if p['squareFootage'] <= max_sqft]
    
    return filtered

@app.route('/api/properties')
def get_properties():
    print("hello")
    min_price = request.args.get('minPrice', type=int)
    max_price = request.args.get('maxPrice', type=int)
    bedrooms = request.args.get('bedrooms', type=int)
    bathrooms = request.args.get('bathrooms', type=int)
    property_types = request.args.getlist('propertyType')
    min_sqft = request.args.get('minSqft', type=int)
    max_sqft = request.args.get('maxSqft', type=int)
    if properties_collection is None:
        return jsonify({
            'status': 'error',
            'message': 'Database connection unavailable'
        }), 500
    
    try:
        query = {}
        if min_price or max_price:
            query['price'] = {}
            if min_price:
                query['price']['$gte'] = min_price
            if max_price:
                query['price']['$lte'] = max_price
        
        if bedrooms:
            query['bedrooms'] = {'$gte': bedrooms}
        
        if bathrooms:
            query['bathrooms'] = {'$gte': bathrooms}
        
        if property_types:
            query['propertyType'] = {'$in': property_types}
        
        if min_sqft or max_sqft:
            query['squareFootage'] = {}
            if min_sqft:
                query['squareFootage']['$gte'] = min_sqft
            if max_sqft:
                query['squareFootage']['$lte'] = max_sqft
        
        properties = list(properties_collection.find(query, {'_id': 0}))
        return jsonify(properties)
    except Exception as e:
        return {
            'status': 'error',
            'message': f'Failed to fetch properties: {str(e)}'
        }, 500

@app.route('/api/properties/<int:property_id>')
def get_property(property_id):
    if properties_collection is None:
        mock_properties = [
            {
                'id': 1,
                'title': 'Beautiful Family Home',
                'address': '123 Main St, Fremont, CA',
                'city': 'Fremont',
                'state': 'CA',
                'latitude': 37.5485,
                'longitude': -121.9885,
                'price': 750000,
                'bedrooms': 3,
                'bathrooms': 2,
                'squareFootage': 1800,
                'lotSize': 7200,
                'propertyType': 'Single Family Home',
                'imageUrl': 'https://via.placeholder.com/400x300?text=Property+1',
                'yearBuilt': 2015,
                'description': 'This beautiful family home features an open floor plan, modern kitchen with granite countertops, and a spacious backyard perfect for entertaining.',
                'images': [
                    'https://via.placeholder.com/800x600?text=Living+Room',
                    'https://via.placeholder.com/800x600?text=Kitchen',
                    'https://via.placeholder.com/800x600?text=Master+Bedroom',
                    'https://via.placeholder.com/800x600?text=Backyard'
                ],
                'architecturalStyle': 'Contemporary',
                'renovations': 'Kitchen updated 2020, bathrooms renovated 2019',
                'structuralCondition': 'Excellent',
                'hvacAge': 'Central AC/Heat installed 2018',
                'plumbingCondition': 'Updated copper plumbing',
                'electricalSystem': '200-amp electrical panel, updated wiring',
                'flooring': 'Hardwood floors throughout, tile in bathrooms',
                'windows': 'Double-pane windows',
                'insulation': 'R-30 attic insulation',
                'appliances': 'Stainless steel appliances included',
                'smartFeatures': 'Smart thermostat, doorbell camera',
                'parking': '2-car attached garage',
                'propertyTaxes': 9375,
                'hoaFees': 0,
                'utilityEstimate': 180,
                'insuranceCost': 1200,
                'schoolDistrict': 'Fremont Unified School District',
                'commuteTime': '45 minutes to San Francisco',
                'walkScore': 72,
                'crimeRate': 'Below average for the area',
                'amenities': 'Parks, shopping centers, restaurants nearby',
                'ownershipHistory': 'Single owner since construction',
                'zoning': 'Residential R-1',
                'disclosures': 'No known material defects',
                'easements': 'Standard utility easements',
                'hoaRules': 'Not applicable - no HOA',
                'titleStatus': 'Clear title',
                'comparableSales': 'Recent sales: $720K-$780K',
                'timeOnMarket': 14,
                'priceHistory': 'Listed at $750K (current)',
                'neighborhoodTrends': 'Property values increasing 5% annually',
                'inspectionReports': 'Available upon request',
                'pestInspection': 'Clear - no termites or pests found',
                'environmentalHazards': 'None identified',
                'seismicRisk': 'Moderate - standard for Bay Area',
                'appraisalValue': 755000
            },
            {
                'id': 2,
                'title': 'Modern Condo',
                'address': '456 Oak Ave, Fremont, CA',
                'city': 'Fremont',
                'state': 'CA',
                'latitude': 37.5625,
                'longitude': -121.9765,
                'price': 550000,
                'bedrooms': 2,
                'bathrooms': 2,
                'squareFootage': 1200,
                'lotSize': 0,
                'propertyType': 'Condo',
                'imageUrl': 'https://via.placeholder.com/400x300?text=Property+2',
                'yearBuilt': 2018,
                'description': 'Modern condo with high-end finishes, stainless steel appliances, and stunning city views from the balcony.',
                'images': [
                    'https://via.placeholder.com/800x600?text=Living+Area',
                    'https://via.placeholder.com/800x600?text=Kitchen',
                    'https://via.placeholder.com/800x600?text=Bedroom',
                    'https://via.placeholder.com/800x600?text=Balcony+View'
                ],
                'architecturalStyle': 'Modern',
                'renovations': 'Recently built - no renovations needed',
                'structuralCondition': 'Excellent',
                'hvacAge': 'Central AC/Heat - 2018 installation',
                'plumbingCondition': 'New construction plumbing',
                'electricalSystem': 'Modern electrical system',
                'flooring': 'Luxury vinyl plank, tile in bathrooms',
                'windows': 'Floor-to-ceiling windows',
                'insulation': 'High-efficiency insulation',
                'appliances': 'Premium stainless steel appliances',
                'smartFeatures': 'Smart home automation system',
                'parking': '1 assigned parking space',
                'propertyTaxes': 6875,
                'hoaFees': 350,
                'utilityEstimate': 120,
                'insuranceCost': 800,
                'schoolDistrict': 'Fremont Unified School District',
                'commuteTime': '40 minutes to San Francisco',
                'walkScore': 85,
                'crimeRate': 'Low for the area',
                'amenities': 'Gym, pool, concierge service',
                'ownershipHistory': 'Original owner',
                'zoning': 'Residential High-Density',
                'disclosures': 'No known material defects',
                'easements': 'Standard utility easements',
                'hoaRules': 'Pet-friendly, quiet hours 10pm-7am',
                'titleStatus': 'Clear title',
                'comparableSales': 'Recent sales: $520K-$580K',
                'timeOnMarket': 8,
                'priceHistory': 'Listed at $550K (current)',
                'neighborhoodTrends': 'Condo values stable',
                'inspectionReports': 'Available upon request',
                'pestInspection': 'Clear - no issues found',
                'environmentalHazards': 'None identified',
                'seismicRisk': 'Low - modern construction',
                'appraisalValue': 560000
            },
            {
                'id': 3,
                'title': 'Luxury Townhouse',
                'address': '789 Pine St, Fremont, CA',
                'city': 'Fremont',
                'state': 'CA',
                'latitude': 37.5345,
                'longitude': -121.9995,
                'price': 1200000,
                'bedrooms': 4,
                'bathrooms': 3,
                'squareFootage': 2400,
                'propertyType': 'Townhouse',
                'imageUrl': 'https://via.placeholder.com/400x300?text=Property+3',
                'yearBuilt': 2020,
                'description': 'Spacious townhouse with contemporary design, private garage, and premium location near schools and shopping.',
                'images': [
                    'https://via.placeholder.com/800x600?text=Front+Entrance',
                    'https://via.placeholder.com/800x600?text=Living+Room',
                    'https://via.placeholder.com/800x600?text=Master+Suite',
                    'https://via.placeholder.com/800x600?text=Garage'
                ]
            },
            {
                'id': 4,
                'title': 'Affordable Starter Home',
                'address': '321 Elm St, Fremont, CA',
                'city': 'Fremont',
                'state': 'CA',
                'latitude': 37.5555,
                'longitude': -121.9725,
                'price': 650000,
                'bedrooms': 2,
                'bathrooms': 1,
                'squareFootage': 1000,
                'propertyType': 'Single Family Home',
                'imageUrl': 'https://via.placeholder.com/400x300?text=Property+4',
                'yearBuilt': 1995,
                'description': 'Charming starter home with original hardwood floors, updated kitchen, and a cozy fireplace in the living room.',
                'images': [
                    'https://via.placeholder.com/800x600?text=Living+Room',
                    'https://via.placeholder.com/800x600?text=Kitchen',
                    'https://via.placeholder.com/800x600?text=Bedroom',
                    'https://via.placeholder.com/800x600?text=Front+Yard'
                ]
            },
            {
                'id': 5,
                'title': 'Executive Estate',
                'address': '555 Luxury Lane, Fremont, CA',
                'city': 'Fremont',
                'state': 'CA',
                'latitude': 37.5415,
                'longitude': -121.9855,
                'price': 1850000,
                'bedrooms': 5,
                'bathrooms': 4,
                'squareFootage': 3500,
                'propertyType': 'Single Family Home',
                'imageUrl': 'https://via.placeholder.com/400x300?text=Property+5',
                'yearBuilt': 2022,
                'description': 'Stunning luxury estate with premium finishes throughout, gourmet kitchen, wine cellar, and resort-style backyard with pool.',
                'images': [
                    'https://via.placeholder.com/800x600?text=Grand+Entrance',
                    'https://via.placeholder.com/800x600?text=Gourmet+Kitchen',
                    'https://via.placeholder.com/800x600?text=Master+Suite',
                    'https://via.placeholder.com/800x600?text=Pool+Area'
                ]
            }
        ]
        
        property_data = next((prop for prop in mock_properties if prop['id'] == property_id), None)
        print(property_data)
        if property_data:
            return jsonify(property_data)
        else:
            return {'status': 'error', 'message': 'Property not found'}, 404
    
    try:
        property_data = properties_collection.find_one({'id': property_id}, {'_id': 0})
        if property_data:
            return jsonify(property_data)
        else:
            return {
                'status': 'error',
                'message': 'Property not found'
            }, 404
    except Exception as e:
        return {
            'status': 'error',
            'message': f'Failed to fetch property: {str(e)}'
        }, 500

@app.route('/api/verify-identity', methods=['POST'])
def verify_identity():
    try:
        for file_key in ['governmentId', 'proofOfAddress']:
            if file_key in request.files:
                file = request.files[file_key]
                if file.content_length and file.content_length > MAX_FILE_SIZE:
                    return jsonify({'message': f'{file_key} file size exceeds 10MB limit'}), 400

        verification_data = {
            'id': str(uuid.uuid4()),
            'firstName': request.form.get('firstName'),
            'lastName': request.form.get('lastName'),
            'email': request.form.get('email'),
            'phone': request.form.get('phone'),
            'address': request.form.get('address'),
            'status': 'pending',
            'submittedAt': datetime.now().isoformat()
        }

        if 'governmentId' in request.files:
            gov_id_file = request.files['governmentId']
            gov_id_path = save_file(gov_id_file, 'gov_id')
            if gov_id_path:
                verification_data['governmentIdPath'] = gov_id_path
            else:
                return jsonify({'message': 'Invalid government ID file'}), 400

        if 'proofOfAddress' in request.files:
            address_file = request.files['proofOfAddress']
            address_path = save_file(address_file, 'proof_address')
            if address_path:
                verification_data['proofOfAddressPath'] = address_path
            else:
                return jsonify({'message': 'Invalid proof of address file'}), 400

        if verifications_collection is not None:
            result = verifications_collection.insert_one(verification_data)
            verification_data['_id'] = str(result.inserted_id)
        else:
            return jsonify({'message': 'Database connection unavailable'}), 500

        return jsonify({
            'message': 'Identity verification submitted successfully',
            'verification': {
                'id': verification_data['id'],
                'status': verification_data['status']
            }
        }), 201

    except Exception as e:
        return jsonify({'message': f'Error processing verification: {str(e)}'}), 500

@app.route('/api/listings', methods=['POST'])
def create_listing():
    try:
        auth_header = request.headers.get('Authorization')
        user_id = None
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header[7:]
            payload = verify_jwt_token(token)
            if payload:
                user_id = payload['user_id']

        listing_data = {
            'id': str(uuid.uuid4()),
            'title': request.form.get('title'),
            'address': request.form.get('address'),
            'city': request.form.get('city'),
            'state': request.form.get('state'),
            'zipCode': request.form.get('zipCode'),
            'price': request.form.get('price'),
            'bedrooms': request.form.get('bedrooms'),
            'bathrooms': request.form.get('bathrooms'),
            'squareFootage': request.form.get('squareFootage'),
            'propertyType': request.form.get('propertyType'),
            'description': request.form.get('description'),
            'createdAt': datetime.now().isoformat(),
            'userId': user_id,
            'photos': [],
            'videos': [],
            'titleDeed': None
        }

        photo_files = []
        for key in request.files:
            if key.startswith('photo_'):
                photo_files.append(request.files[key])

        for i, photo_file in enumerate(photo_files):
            if photo_file.content_length and photo_file.content_length > MAX_FILE_SIZE:
                return jsonify({'message': f'Photo {i+1} file size exceeds 10MB limit'}), 400
            
            photo_path = save_file(photo_file, f'listing_photo_{i}')
            if photo_path:
                listing_data['photos'].append(photo_path)
            else:
                return jsonify({'message': f'Invalid photo file: {photo_file.filename}'}), 400

        video_files = []
        for key in request.files:
            if key.startswith('video_'):
                video_files.append(request.files[key])

        for i, video_file in enumerate(video_files):
            if video_file.content_length and video_file.content_length > MAX_FILE_SIZE:
                return jsonify({'message': f'Video {i+1} file size exceeds 10MB limit'}), 400
            
            video_path = save_file(video_file, f'listing_video_{i}')
            if video_path:
                listing_data['videos'].append(video_path)
            else:
                return jsonify({'message': f'Invalid video file: {video_file.filename}'}), 400

        if 'title_deed' in request.files:
            title_deed_file = request.files['title_deed']
            if title_deed_file.content_length and title_deed_file.content_length > MAX_FILE_SIZE:
                return jsonify({'message': 'Title deed file size exceeds 10MB limit'}), 400
            
            title_deed_path = save_file(title_deed_file, 'title_deed')
            if title_deed_path:
                listing_data['titleDeed'] = title_deed_path

        if not listing_data['photos']:
            return jsonify({'message': 'At least one photo is required'}), 400

        if listings_collection is not None:
            result = listings_collection.insert_one(listing_data)
            listing_data['_id'] = str(result.inserted_id)
        else:
            return jsonify({'message': 'Database connection unavailable'}), 500

        return jsonify({
            'message': 'Property listing created successfully',
            'listing': listing_data
        }), 201

    except Exception as e:
        return jsonify({'message': f'Error creating listing: {str(e)}'}), 500

@app.route('/api/listings', methods=['GET'])
def get_listings():
    try:
        if listings_collection is None:
            return jsonify({'message': 'Database connection unavailable'}), 500
            
        listings_list = list(listings_collection.find({}, {'_id': 0}))
        return jsonify({
            'listings': listings_list,
            'count': len(listings_list)
        }), 200
    except Exception as e:
        return jsonify({'message': f'Error fetching listings: {str(e)}'}), 500

@app.route('/api/listings/<listing_id>', methods=['GET'])
def get_listing(listing_id):
    try:
        if listings_collection is None:
            return jsonify({'message': 'Database connection unavailable'}), 500
            
        listing = listings_collection.find_one({'id': listing_id}, {'_id': 0})
        if not listing:
            return jsonify({'message': 'Listing not found'}), 404
        
        return jsonify({
            'listing': listing
        }), 200
    except Exception as e:
        return jsonify({'message': f'Error fetching listing: {str(e)}'}), 500
        
# Explicit preflight support for /api/signup (blueprint route)
@app.route('/api/signup', methods=['OPTIONS'])
def signup_options():
    return ('', 204)

# Renamed legacy duplicate route to avoid overriding auth blueprint
@app.route('/legacy-api/signup', methods=['POST'])
def signup():
    if users_collection is None:
        return jsonify({
            'success': False,
            'message': 'Database connection unavailable'
        }), 500
    
    try:
        data = request.get_json()
        
        required_fields = ['firstName', 'lastName', 'email', 'password']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'{field} is required'
                }), 400
        
        if not validate_email(data['email']):
            return jsonify({
                'success': False,
                'message': 'Invalid email format'
            }), 400
        
        if len(data['password']) < 6:
            return jsonify({
                'success': False,
                'message': 'Password must be at least 6 characters long'
            }), 400
        
        existing_user = users_collection.find_one({'email': data['email'].lower()})
        if existing_user:
            return jsonify({
                'success': False,
                'message': 'User with this email already exists'
            }), 409
        
        # Hash the password and store as BSON Binary for consistency
        raw_hash = bcrypt.hashpw(data['password'].encode('utf-8'), bcrypt.gensalt(rounds=12))
        password_hash = Binary(raw_hash)
        
        user_doc = {
            'firstName': data['firstName'].strip(),
            'lastName': data['lastName'].strip(),
            'email': data['email'].lower().strip(),
            'password': password_hash,
            'createdAt': datetime.utcnow()
        }
        
        result = users_collection.insert_one(user_doc)
        
        token = generate_jwt_token(result.inserted_id, data['email'].lower())
        
        return jsonify({
            'success': True,
            'message': 'User registered successfully',
            'user': {
                'id': str(result.inserted_id),
                'firstName': data['firstName'],
                'lastName': data['lastName'],
                'email': data['email'].lower()
            },
            'token': token
        }), 201
        
    except Exception as e:
        print(f"Signup error: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500

# Explicit preflight support for /api/login (blueprint route)
@app.route('/api/login', methods=['OPTIONS'])
def login_options():
    # Respond OK to CORS preflight
    return ('', 204)

# Renamed legacy duplicate route to avoid overriding auth blueprint
@app.route('/legacy-api/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        
        if not data.get('email') or not data.get('password'):
            return jsonify({
                'success': False,
                'message': 'Email and password are required'
            }), 400
        
        if users_collection is None:
            return jsonify({
                'success': False,
                'message': 'Database connection unavailable'
            }), 500
        
        # Normalize email by stripping whitespace and converting to lowercase
        normalized_email = data['email'].strip().lower()
        user = users_collection.find_one({'email': normalized_email})
        print(f"Login attempt for email: {normalized_email}")
        print(f"User found: {user is not None}")
        if not user:
            return jsonify({
                'success': False,
                'message': 'Invalid email or password'
            }), 401
        
        stored_hash = user['password']
        # stored_hash could be BSON Binary, bytes or str
        if isinstance(stored_hash, Binary):
            stored_hash = bytes(stored_hash)
        elif isinstance(stored_hash, str):
            stored_hash = stored_hash.encode('utf-8')
        password_match = bcrypt.checkpw(data['password'].encode('utf-8'), stored_hash)
        print(f"Password match (app route): {password_match}")
        if not password_match:
            return jsonify({
                'success': False,
                'message': 'Invalid email or password'
            }), 401
        
        token = generate_jwt_token(user['_id'], user['email'])
        
        return jsonify({
            'success': True,
            'message': 'Login successful',
            'user': {
                'id': str(user['_id']),
                'firstName': user['firstName'],
                'lastName': user['lastName'],
                'email': user['email']
            },
            'token': token
        }), 200
        
    except Exception as e:
        print(f"Login error: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500


@app.route('/api/profile', methods=['GET'])
@require_auth
def get_profile():
    try:
        test_user_id = '000000000000000000000000'
        
        if users_collection is None:
            return jsonify({'message': 'Database connection unavailable'}), 500
        
        try:
            user_id = getattr(request, 'current_user_id', test_user_id)
            user = users_collection.find_one({'_id': ObjectId(user_id)})
            if not user:
                return jsonify({'message': 'User not found'}), 404
            
            profile_data = {
                'id': str(user['_id']),
                'firstName': user.get('firstName', ''),
                'lastName': user.get('lastName', ''),
                'email': user.get('email', ''),
                'phone': user.get('phone', ''),
                'bio': user.get('bio', ''),
                'profilePicture': user.get('profilePicture', '')
            }
            
            return jsonify(profile_data), 200
        except Exception as mongo_err:
            print(f"MongoDB error: {mongo_err}")
            profile_data = {
                'id': test_user_id,
                'firstName': 'Test',
                'lastName': 'User',
                'email': '<EMAIL>',
                'phone': '(*************',
                'bio': '',
                'profilePicture': ''
            }
            return jsonify(profile_data), 200
            
    except Exception as e:
        print(f"Get profile error: {e}")
        return jsonify({'message': 'Internal server error'}), 500

@app.route('/api/profile', methods=['PUT'])
@require_auth
def update_profile():
    try:
        update_data = {}
        
        if 'firstName' in request.form:
            update_data['firstName'] = request.form['firstName'].strip()
        if 'lastName' in request.form:
            update_data['lastName'] = request.form['lastName'].strip()
        if 'email' in request.form:
            email = request.form['email'].lower().strip()
            if not validate_email(email):
                return jsonify({'message': 'Invalid email format'}), 400
            
            if users_collection is not None:
                existing_user = users_collection.find_one({
                    'email': email,
                    '_id': {'$ne': ObjectId(request.current_user_id)}
                })
                if existing_user:
                    return jsonify({'message': 'Email already in use'}), 409
            
            update_data['email'] = email
        
        if 'phone' in request.form:
            update_data['phone'] = request.form['phone'].strip()
        if 'bio' in request.form:
            update_data['bio'] = request.form['bio'].strip()
        
        if 'profilePicture' in request.files:
            profile_picture = request.files['profilePicture']
            if profile_picture.content_length and profile_picture.content_length > MAX_FILE_SIZE:
                return jsonify({'message': 'Profile picture file size exceeds 10MB limit'}), 400
            
            try:
                old_picture_url = None
                
                test_user_id = '000000000000000000000000'
                
                if users_collection is not None:
                    try:
                        user_id = getattr(request, 'current_user_id', test_user_id)
                        current_user = users_collection.find_one({'_id': ObjectId(user_id)})
                        if current_user and current_user.get('profilePicture'):
                            old_picture_url = current_user['profilePicture']
                    except Exception as mongo_err:
                        print(f"MongoDB error: {mongo_err}")
                
                user_id = getattr(request, 'current_user_id', test_user_id)
                print(f"Uploading profile picture for user: {user_id}")
                
                picture_url = save_file(profile_picture, 'profile', user_id)
                print(f"Profile picture URL: {picture_url}")
                
                if picture_url:
                    update_data['profilePicture'] = picture_url
                    
                    if old_picture_url:
                        storage_service.delete_profile_picture(old_picture_url)
                else:
                    return jsonify({'message': 'Invalid profile picture file'}), 400
            except Exception as e:
                print(f"Profile picture upload error: {e}")
                return jsonify({'message': f'Failed to upload profile picture: {str(e)}'}), 400
        
        test_user_id = '000000000000000000000000'
        user_id = getattr(request, 'current_user_id', test_user_id)
        
        if users_collection is not None and update_data:
            update_data['updatedAt'] = datetime.utcnow()
            
            try:
                result = users_collection.update_one(
                    {'_id': ObjectId(user_id)},
                    {'$set': update_data}
                )
                
                if result.modified_count == 0 and not 'profilePicture' in update_data:
                    return jsonify({'message': 'No changes made'}), 200
                
                updated_user = users_collection.find_one({'_id': ObjectId(user_id)})
                
                user_data = {
                    'id': str(updated_user['_id']),
                    'firstName': updated_user.get('firstName', ''),
                    'lastName': updated_user.get('lastName', ''),
                    'email': updated_user.get('email', ''),
                    'phone': updated_user.get('phone', ''),
                    'bio': updated_user.get('bio', ''),
                    'profilePicture': updated_user.get('profilePicture', '')
                }
            except Exception as mongo_err:
                print(f"MongoDB update error: {mongo_err}")
                user_data = {
                    'id': user_id,
                    'firstName': update_data.get('firstName', 'Test'),
                    'lastName': update_data.get('lastName', 'User'),
                    'email': update_data.get('email', '<EMAIL>'),
                    'phone': update_data.get('phone', ''),
                    'bio': update_data.get('bio', ''),
                    'profilePicture': update_data.get('profilePicture', '')
                }
        else:
            user_data = {
                'id': user_id,
                'firstName': update_data.get('firstName', 'Test'),
                'lastName': update_data.get('lastName', 'User'),
                'email': update_data.get('email', '<EMAIL>'),
                'phone': update_data.get('phone', ''),
                'bio': update_data.get('bio', ''),
                'profilePicture': update_data.get('profilePicture', '')
            }
        
        return jsonify({
            'message': 'Profile updated successfully',
            'user': user_data
        }), 200
        
    except Exception as e:
        print(f"Update profile error: {e}")
        return jsonify({'message': 'Internal server error'}), 500

@app.route('/api/user/listings', methods=['GET'])
@require_auth
def get_user_listings():
    try:
        if listings_collection is None:
            return jsonify({'message': 'Database connection unavailable'}), 500
        
        user_listings = list(listings_collection.find(
            {'userId': request.current_user_id},
            {'_id': 0}
        ).sort('createdAt', -1))
        
        return jsonify({
            'listings': user_listings,
            'count': len(user_listings)
        }), 200
        
    except Exception as e:
        print(f"Get user listings error: {e}")
        return jsonify({'message': 'Internal server error'}), 500

@app.route('/api/listings/<listing_id>', methods=['DELETE'])
@require_auth
def delete_listing(listing_id):
    if listings_collection is None:
        return jsonify({'message': 'Database connection unavailable'}), 500
    
    try:
        listing = listings_collection.find_one({'id': listing_id})
        if not listing:
            return jsonify({'message': 'Listing not found'}), 404
        
        if listing.get('userId') != request.current_user_id:
            return jsonify({'message': 'Unauthorized to delete this listing'}), 403
        
        result = listings_collection.delete_one({'id': listing_id})
        
        if result.deleted_count == 0:
            return jsonify({'message': 'Failed to delete listing'}), 500
        
        return jsonify({'message': 'Listing deleted successfully'}), 200
        
    except Exception as e:
        print(f"Delete listing error: {e}")
        return jsonify({'message': 'Internal server error'}), 500

@app.route('/api/change-password', methods=['PUT'])
@require_auth
def change_password():
    if users_collection is None:
        return jsonify({'message': 'Database connection unavailable'}), 500
    
    try:
        data = request.get_json()
        
        if not data.get('currentPassword') or not data.get('newPassword'):
            return jsonify({'message': 'Current password and new password are required'}), 400
        
        user = users_collection.find_one({'_id': ObjectId(request.current_user_id)})
        if not user:
            return jsonify({'message': 'User not found'}), 404
        
        if not bcrypt.checkpw(data['currentPassword'].encode('utf-8'), user['password']):
            return jsonify({'message': 'Current password is incorrect'}), 401
        
        if len(data['newPassword']) < 6:
            return jsonify({'message': 'New password must be at least 6 characters long'}), 400
        
        new_password_hash = bcrypt.hashpw(data['newPassword'].encode('utf-8'), bcrypt.gensalt(rounds=12))
        
        users_collection.update_one(
            {'_id': ObjectId(request.current_user_id)},
            {'$set': {'password': new_password_hash, 'updatedAt': datetime.utcnow()}}
        )
        
        return jsonify({'message': 'Password changed successfully'}), 200
        
    except Exception as e:
        print(f"Change password error: {e}")
        return jsonify({'message': 'Internal server error'}), 500

@app.route('/api/preferences', methods=['PUT'])
@require_auth
def update_preferences():
    if users_collection is None:
        return jsonify({'message': 'Database connection unavailable'}), 500
    
    try:
        data = request.get_json()
        
        preferences = {
            'emailNotifications': data.get('emailNotifications', True),
            'smsNotifications': data.get('smsNotifications', False),
            'marketingEmails': data.get('marketingEmails', True)
        }
        
        users_collection.update_one(
            {'_id': ObjectId(request.current_user_id)},
            {'$set': {'preferences': preferences, 'updatedAt': datetime.utcnow()}}
        )
        
        return jsonify({'message': 'Preferences updated successfully'}), 200
        
    except Exception as e:
        print(f"Update preferences error: {e}")
        return jsonify({'message': 'Internal server error'}), 500

@app.route('/api/listings/<listing_id>/messages', methods=['GET'])
@require_auth
def get_listing_messages(listing_id):
    try:
        if listings_collection is None or messages_collection is None:
            return jsonify({'message': 'Database connection unavailable'}), 500
        
        listing = listings_collection.find_one({'id': listing_id})
        if not listing:
            return jsonify({'message': 'Listing not found'}), 404
        
        if listing.get('userId') != request.current_user_id:
            return jsonify({'message': 'Unauthorized to view messages for this listing'}), 403
        
        messages = list(messages_collection.find(
            {'listingId': listing_id},
            {'_id': 0}
        ).sort('createdAt', 1))
        
        return jsonify({'messages': messages}), 200
        
    except Exception as e:
        print(f"Get listing messages error: {e}")
        return jsonify({'message': 'Internal server error'}), 500

@app.route('/api/listings/<listing_id>/messages', methods=['POST'])
@require_auth
def send_listing_message(listing_id):
    try:
        data = request.get_json()
        if not data.get('message'):
            return jsonify({'message': 'Message content is required'}), 400
        
        if listings_collection is None or messages_collection is None:
            return jsonify({'message': 'Database connection unavailable'}), 500
        
        listing = listings_collection.find_one({'id': listing_id})
        if not listing:
            return jsonify({'message': 'Listing not found'}), 404
        
        user = users_collection.find_one({'_id': ObjectId(request.current_user_id)})
        if not user:
            return jsonify({'message': 'User not found'}), 404
        
        message = {
            'id': str(uuid.uuid4()),
            'listingId': listing_id,
            'senderId': request.current_user_id,
            'senderName': user.get('name', 'Anonymous'),
            'message': data['message'].strip(),
            'createdAt': datetime.utcnow(),
            'isOwner': listing.get('userId') == request.current_user_id
        }
        
        messages_collection.insert_one(message)
        
        message.pop('_id', None)
        return jsonify({'message': message}), 201
        
    except Exception as e:
        print(f"Send listing message error: {e}")
        return jsonify({'message': 'Internal server error'}), 500

@app.route('/api/delete-account', methods=['DELETE'])
@require_auth
def delete_account():
    if users_collection is None or listings_collection is None:
        return jsonify({'message': 'Database connection unavailable'}), 500
    
    try:
        listings_collection.delete_many({'userId': request.current_user_id})
        
        result = users_collection.delete_one({'_id': ObjectId(request.current_user_id)})
        
        if result.deleted_count == 0:
            return jsonify({'message': 'Failed to delete account'}), 500
        
        return jsonify({'message': 'Account deleted successfully'}), 200
        
    except Exception as e:
        print(f"Delete account error: {e}")
        return jsonify({'message': 'Internal server error'}), 500

@app.route('/api/send-test-email', methods=['POST'])
@require_auth
def send_test_email():
    try:
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart
        
        smtp_server = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
        smtp_port = int(os.getenv('SMTP_PORT', '587'))
        smtp_username = os.getenv('SMTP_USERNAME')
        smtp_password = os.getenv('SMTP_PASSWORD')
        
        if not smtp_username or not smtp_password:
            return jsonify({
                'success': False,
                'message': 'Email configuration not set up'
            }), 500
        
        msg = MIMEMultipart()
        msg['From'] = smtp_username
        msg['To'] = request.current_user_email
        msg['Subject'] = 'OneMLS Test Email'
        
        body = f"""Hello!

This is a test email from your OneMLS account.

Sent at: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC

If you received this email, your email functionality is working correctly.

Best regards,
OneMLS Team"""
        
        msg.attach(MIMEText(body, 'plain'))
        
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(smtp_username, smtp_password)
        server.send_message(msg)
        server.quit()
        
        return jsonify({
            'success': True,
            'message': f'Test email sent successfully to {request.current_user_email}'
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to send test email: {str(e)}'
        }), 500


@app.route('/api/chat/stream', methods=['POST'])
def chat_stream():
    """
    Streaming chat endpoint using Gemini 1.5 API with conversation history
    """
    try:
        data = request.get_json()
        message = data.get('message', '')
        properties = data.get('properties', [])
        conversation_id = data.get('conversationId', '')
        
        # Create a new conversation ID if not provided
        if not conversation_id:
            conversation_id = str(uuid.uuid4())
            
        # Initialize in-memory conversation storage if it doesn't exist
        if not hasattr(app, 'conversations'):
            app.conversations = {}
            
        # Get or create conversation history
        if conversation_id not in app.conversations:
            app.conversations[conversation_id] = []
            
        # Get conversation history
        history = app.conversations[conversation_id]
        
        # Add user message to history
        history.append({"role": "user", "content": message})
        
        # Limit history length to prevent token overflow
        if len(history) > 10:
            history = history[-10:]
            app.conversations[conversation_id] = history
            
    except Exception as e:
        return Response(
            f"data: {json.dumps({'content': 'AI error! Please try again later', 'done': True})}\n\n",
            mimetype='text/plain',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type',
            }
        )
    
    def generate_response(message, properties, history, conversation_id):
        try:
            system_prompt = """You are called MLS Agent, a knowledgeable and friendly real estate help agent. Your role is to assist customers with any questions or issues related to real estate, including buying, selling, renting, property listings, and understanding the Multiple Listing Service (MLS). Always be professional, patient, and solution-oriented.

When discussing properties, provide specific insights about:
- Market trends and pricing analysis
- Neighborhood characteristics and amenities
- Property comparisons and recommendations

Keep responses concise when possible. Always aim to be helpful and solution-oriented.
Don't ask that many sub questions

"""
            
            property_context = ""
            if properties:
                property_context = f"\n\nCurrent properties being viewed: {len(properties)} properties available for analysis."
                for i, prop in enumerate(properties[:3]):  # Limit to first 3 properties
                    property_context += f"\nProperty {i+1}: {prop.get('address', 'Unknown address')} - ${prop.get('price', 0):,} - {prop.get('bedrooms', 0)} bed/{prop.get('bathrooms', 0)} bath"
            
            # Format conversation history
            conversation_history = ""
            for entry in history[:-1]:  # Exclude the current message which we'll add separately
                role = entry.get("role", "")
                content = entry.get("content", "")
                conversation_history += f"\n{role.capitalize()}: {content}"
            
            # Combine all context
            full_prompt = f"{system_prompt}{property_context}\n\nConversation history:{conversation_history}\n\nUser: {message}"
            
            if not gemini_api_key:
                yield f"data: {json.dumps({'content': 'AI error! Please try again later', 'done': True, 'conversationId': conversation_id})}\n\n"
                return
            
            try:
                model = genai.GenerativeModel('gemini-1.5-flash')
                response = model.generate_content(
                    full_prompt,
                    stream=True,
                    generation_config=genai.types.GenerationConfig(
                        temperature=0.7,
                        max_output_tokens=1000,
                    )
                )
                
                # Collect the full response
                full_response = ""
                
                for chunk in response:
                    if chunk.text:
                        full_response += chunk.text
                        yield f"data: {json.dumps({'content': chunk.text, 'done': False, 'conversationId': conversation_id})}\n\n"
                        time.sleep(0.05)  # Small delay for better streaming effect
                
                # Add assistant's response to history
                app.conversations[conversation_id].append({"role": "assistant", "content": full_response})
                
                yield f"data: {json.dumps({'content': '', 'done': True, 'conversationId': conversation_id})}\n\n"
                
            except Exception as gemini_error:
                print(f"Gemini API error: {gemini_error}")
                yield f"data: {json.dumps({'content': 'AI error! Please try again later', 'done': True, 'conversationId': conversation_id})}\n\n"
            
        except Exception as e:
            yield f"data: {json.dumps({'content': 'AI error! Please try again later', 'done': True, 'conversationId': conversation_id})}\n\n"
    
    return Response(
        generate_response(message, properties, history, conversation_id),
        mimetype='text/plain',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type',
        }
    )

@app.route('/api/chat', methods=['POST'])
def chat_assistant():
    """
    Legacy chat endpoint - redirects to streaming endpoint
    """
    try:
        data = request.get_json()
        message = data.get('message', '')
        
        response = {
            'message': 'Please use the /api/chat/stream endpoint for real-time responses.',
            'timestamp': datetime.utcnow().isoformat(),
            'redirect': '/api/chat/stream'
        }
        
        return jsonify(response), 200
        
    except Exception as e:
        return jsonify({
            'error': 'Failed to process chat message',
            'message': str(e)
        }), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5004)
