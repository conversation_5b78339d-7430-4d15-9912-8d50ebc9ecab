#!/usr/bin/env python3

import bcrypt
import os
from datetime import datetime
from pymongo import MongoClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def create_test_user():
    """Create a test user for login testing"""

    # Initialize MongoDB connection (same as in application.py)
    mongodb_uri = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/one_mls_db')

    try:
        client = MongoClient(
            mongodb_uri,
            serverSelectionTimeoutMS=10000,
            tls=True,
            tlsAllowInvalidCertificates=True
        )

        # Extract database name from URI or use default
        if 'one_mls_db' in mongodb_uri:
            db = client.one_mls_db
        else:
            db = client.one_mls_db

        users_collection = db.users

        # Test connection
        client.admin.command('ping')
        print("MongoDB connection successful")

    except Exception as e:
        print(f"MongoDB connection failed: {e}")
        return False

    # Test user credentials (matching the frontend defaults)
    test_email = "<EMAIL>"
    test_password = "testtest"

    try:
        # Check if user already exists
        existing_user = users_collection.find_one({'email': test_email.lower()})
        if existing_user:
            print(f"Test user {test_email} already exists")
            return True

        # Hash the password
        password_hash = bcrypt.hashpw(test_password.encode('utf-8'), bcrypt.gensalt(rounds=12))

        # Create user document
        user_doc = {
            'firstName': 'Test',
            'lastName': 'User',
            'email': test_email.lower(),
            'password': password_hash,
            'createdAt': datetime.utcnow()
        }

        # Insert the user
        result = users_collection.insert_one(user_doc)
        print(f"Successfully created test user: {test_email}")
        print(f"User ID: {result.inserted_id}")

        # Verify the user was created
        user_count = users_collection.count_documents({})
        print(f"Total users in database: {user_count}")

        return True

    except Exception as e:
        print(f"Error creating test user: {str(e)}")
        return False

if __name__ == "__main__":
    success = create_test_user()
    if success:
        print("Test user creation completed successfully!")
        print("You can now login with:")
        print("Email: <EMAIL>")
        print("Password: testtest")
    else:
        print("Test user creation failed!")
