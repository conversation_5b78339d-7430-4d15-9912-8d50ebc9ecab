import os
from pymongo import MongoClient
from dotenv import load_dotenv

load_dotenv()

class DatabaseConnection:
    def __init__(self):
        self.mongodb_uri = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/one_mls_db')
        self.client = None
        self.db = None
        self.users_collection = None
        self.properties_collection = None
        self.verifications_collection = None
        self.listings_collection = None
        self.messages_collection = None
        self.saved_listings_collection = None
        
        self._initialize_connection()
    
    def _initialize_connection(self):
        try:
                        # Enable TLS parameters similar to main application connection
            self.client = MongoClient(
            self.mongodb_uri,
            serverSelectionTimeoutMS=10000,
            tls=True,
                            tlsAllowInvalidCertificates=True
            )
            if 'one_mls_db' in self.mongodb_uri:
                self.db = self.client.one_mls_db
            else:
                self.db = self.client.one_mls_db
            
            self.users_collection = self.db.users
            self.properties_collection = self.db.properties
            self.verifications_collection = self.db.verifications
            self.listings_collection = self.db.listings
            self.messages_collection = self.db.messages
            self.saved_listings_collection = self.db.saved_listings
            
            # Verify connection
            self.client.admin.command('ping')
            print("MongoDB connection successful")
            
            try:
                self.users_collection.create_index("email", unique=True)
                self.verifications_collection.create_index("email")
                self.verifications_collection.create_index("id")
                self.listings_collection.create_index("id")
                self.listings_collection.create_index([("city", 1), ("state", 1)])
                self.listings_collection.create_index("propertyType")
                self.messages_collection.create_index("listingId")
                self.messages_collection.create_index("createdAt")
                self.saved_listings_collection.create_index([("userId", 1), ("propertyId", 1)], unique=True)
                self.saved_listings_collection.create_index("userId")
                print("MongoDB indexes created successfully")
            except Exception as e:
                print(f"Warning: Could not create indexes: {e}")
                
        except Exception as e:
            print(f"MongoDB connection failed: {e}")
            print("Server will start without database connection")
            self.client = None
            self.db = None
            self.users_collection = None
            self.properties_collection = None
            self.verifications_collection = None
            self.listings_collection = None
            self.messages_collection = None
            self.saved_listings_collection = None
    
    def get_collections(self):
        return {
            'users': self.users_collection,
            'properties': self.properties_collection,
            'verifications': self.verifications_collection,
            'listings': self.listings_collection,
            'messages': self.messages_collection,
            'saved_listings': self.saved_listings_collection
        }
    
    def get_client(self):
        return self.client
    
    def get_db(self):
        return self.db
