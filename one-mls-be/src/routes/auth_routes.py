from flask import Blueprint, request, jsonify
from bson import ObjectId
from datetime import datetime, timedelta
import bcrypt
import jwt
import re
import os
from flask_cors import CORS

auth_bp = Blueprint('auth', __name__)
CORS(auth_bp)

def validate_email(email):
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def generate_jwt_token(user_id, email, secret_key):
    payload = {
        'user_id': str(user_id),
        'email': email,
        'exp': datetime.utcnow() + timedelta(hours=24)
    }
    return jwt.encode(payload, secret_key, algorithm='HS256')

def init_auth_routes(db_connection):
    print("Initializing auth routes blueprint")
    collections = db_connection.get_collections()
    users_collection = collections['users']
    secret_key = os.getenv('JWT_SECRET_KEY', 'your-secret-key-change-in-production')

    @auth_bp.route('/api/signup', methods=['POST'])
    def signup():
        if users_collection is None:
            return jsonify({
                'success': False,
                'message': 'Database connection unavailable'
            }), 500

        try:
            data = request.get_json()

            # Validate required fields
            required_fields = ['firstName', 'lastName', 'email', 'password']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({
                        'success': False,
                        'message': f'{field} is required'
                    }), 400

            # Validate email format
            if not validate_email(data['email']):
                return jsonify({
                    'success': False,
                    'message': 'Invalid email format'
                }), 400

            # Validate password length
            if len(data['password']) < 6:
                return jsonify({
                    'success': False,
                    'message': 'Password must be at least 6 characters long'
                }), 400

            # Check if user already exists
            email = data['email'].lower().strip()
            existing_user = users_collection.find_one({'email': email})
            if existing_user:
                return jsonify({
                    'success': False,
                    'message': 'User with this email already exists'
                }), 409

            # Hash password
            password_hash = bcrypt.hashpw(data['password'].encode('utf-8'), bcrypt.gensalt())

            # Create user document
            user_doc = {
                'firstName': data['firstName'].strip(),
                'lastName': data['lastName'].strip(),
                'email': email,
                'password': password_hash,
                'createdAt': datetime.utcnow()
            }

            # Insert user
            result = users_collection.insert_one(user_doc)

            # Generate token
            token = generate_jwt_token(result.inserted_id, email, secret_key)

            return jsonify({
                'success': True,
                'message': 'User registered successfully',
                'user': {
                    'id': str(result.inserted_id),
                    'firstName': data['firstName'],
                    'lastName': data['lastName'],
                    'email': email
                },
                'token': token
            }), 201

        except Exception as e:
            print(f"Signup error: {e}")
            return jsonify({
                'success': False,
                'message': 'Internal server error'
            }), 500

    @auth_bp.route('/api/login', methods=['POST'])
    def login():
        try:
            data = request.get_json()

            # Validate required fields
            if not data.get('email') or not data.get('password'):
                return jsonify({
                    'success': False,
                    'message': 'Email and password are required'
                }), 400

            # Check database connection
            if users_collection is None:
                return jsonify({
                    'success': False,
                    'message': 'Database connection unavailable'
                }), 500

            # Find user by email
            email = data['email'].strip().lower()
            user = users_collection.find_one({'email': email})
            if not user:
                return jsonify({
                    'success': False,
                    'message': 'Invalid email or password'
                }), 401

            # Verify password
            stored_hash = user['password']
            if isinstance(stored_hash, bytes):
                password_matches = bcrypt.checkpw(data['password'].encode('utf-8'), stored_hash)
            else:
                # Handle string passwords (shouldn't happen with new implementation)
                password_matches = bcrypt.checkpw(data['password'].encode('utf-8'), stored_hash.encode('utf-8'))

            if not password_matches:
                return jsonify({
                    'success': False,
                    'message': 'Invalid email or password'
                }), 401

            # Generate token
            token = generate_jwt_token(user['_id'], user['email'], secret_key)

            return jsonify({
                'success': True,
                'message': 'Login successful',
                'user': {
                    'id': str(user['_id']),
                    'firstName': user['firstName'],
                    'lastName': user['lastName'],
                    'email': user['email']
                },
                'token': token
            }), 200

        except Exception as e:
            print(f"Login error: {e}")
            return jsonify({
                'success': False,
                'message': 'Internal server error'
            }), 500

    return auth_bp
