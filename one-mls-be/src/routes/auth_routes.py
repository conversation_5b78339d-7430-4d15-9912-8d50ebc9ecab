from flask import Blueprint, request, jsonify
from bson import ObjectId
from datetime import datetime
import bcrypt
from bson.binary import Binary
import os
from src.helpers.auth import validate_email, generate_jwt_token, require_auth

from flask_cors import CORS

auth_bp = Blueprint('auth', __name__)
# Enable CORS (adds automatic OPTIONS route for preflight)
CORS(auth_bp)

def init_auth_routes(db_connection):
    print("Initializing auth routes blueprint")
    collections = db_connection.get_collections()
    users_collection = collections['users']
    listings_collection = collections['listings']
    secret_key = os.getenv('JWT_SECRET_KEY', 'your-secret-key-change-in-production')
    
    @auth_bp.route('/api/signup', methods=['POST'])
    def signup():
        if users_collection is None:
            return jsonify({
                'success': False,
                'message': 'Database connection unavailable'
            }), 500
        
        try:
            data = request.get_json()
            
            required_fields = ['firstName', 'lastName', 'email', 'password']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({
                        'success': False,
                        'message': f'{field} is required'
                    }), 400
            
            if not validate_email(data['email']):
                return jsonify({
                    'success': False,
                    'message': 'Invalid email format'
                }), 400
            
            if len(data['password']) < 6:
                return jsonify({
                    'success': False,
                    'message': 'Password must be at least 6 characters long'
                }), 400
            
            normalized_email = data['email'].strip().lower()
            existing_user = users_collection.find_one({'email': normalized_email})
            if existing_user:
                return jsonify({
                    'success': False,
                    'message': 'User with this email already exists'
                }), 409
            
            # Hash the password and store as BSON Binary to ensure consistent BSON type
            raw_hash = bcrypt.hashpw(data['password'].encode('utf-8'), bcrypt.gensalt(rounds=12))
            password_hash = Binary(raw_hash)
            
            user_doc = {
                'firstName': data['firstName'].strip(),
                'lastName': data['lastName'].strip(),
                'email': data['email'].lower().strip(),
                'password': password_hash,
                'createdAt': datetime.utcnow()
            }
            
            result = users_collection.insert_one(user_doc)
            
            token = generate_jwt_token(result.inserted_id, data['email'].lower(), secret_key)
            
            return jsonify({
                'success': True,
                'message': 'User registered successfully',
                'user': {
                    'id': str(result.inserted_id),
                    'firstName': data['firstName'],
                    'lastName': data['lastName'],
                    'email': data['email'].lower()
                },
                'token': token
            }), 201
            
        except Exception as e:
            print(f"Signup error: {e}")
            return jsonify({
                'success': False,
                'message': 'Internal server error'
            }), 500

    @auth_bp.route('/api/login', methods=['POST'])
    def login():
        try:
            data = request.get_json()
            
            if not data.get('email') or not data.get('password'):
                return jsonify({
                    'success': False,
                    'message': 'Email and password are required'
                }), 400
            
            if users_collection is None:
                return jsonify({
                    'success': False,
                    'message': 'Database connection unavailable'
                }), 500
            
            normalized_email = data['email'].strip().lower()
            user = users_collection.find_one({'email': normalized_email})
            if not user:
                return jsonify({
                    'success': False,
                    'message': 'Invalid email or password'
                }), 401
            
                        stored_hash = user['password']
            # stored_hash could be BSON Binary, bytes or (improperly) str
            if isinstance(stored_hash, Binary):
                stored_hash = bytes(stored_hash)
            elif isinstance(stored_hash, str):
                # Some legacy records might store password as UTF-8 string
                stored_hash = stored_hash.encode('utf-8')
            password_matches = bcrypt.checkpw(data['password'].encode('utf-8'), stored_hash)
            print(f"Password matches DB hash: {password_matches}")
            if not password_matches:
                return jsonify({
                    'success': False,
                    'message': 'Invalid email or password'
                }), 401
            
            token = generate_jwt_token(user['_id'], user['email'], secret_key)
            
            return jsonify({
                'success': True,
                'message': 'Login successful',
                'user': {
                    'id': str(user['_id']),
                    'firstName': user['firstName'],
                    'lastName': user['lastName'],
                    'email': user['email']
                },
                'token': token
            }), 200
            
        except Exception as e:
            print(f"Login error: {e}")
            return jsonify({
                'success': False,
                'message': 'Internal server error'
            }), 500

    @auth_bp.route('/api/change-password', methods=['PUT'])
    @require_auth(secret_key)
    def change_password():
        if users_collection is None:
            return jsonify({'message': 'Database connection unavailable'}), 500
        
        try:
            data = request.get_json()
            
            if not data.get('currentPassword') or not data.get('newPassword'):
                return jsonify({'message': 'Current password and new password are required'}), 400
            
            user = users_collection.find_one({'_id': ObjectId(request.current_user_id)})
            if not user:
                return jsonify({'message': 'User not found'}), 404
            
            if not bcrypt.checkpw(data['currentPassword'].encode('utf-8'), user['password']):
                return jsonify({'message': 'Current password is incorrect'}), 401
            
            if len(data['newPassword']) < 6:
                return jsonify({'message': 'New password must be at least 6 characters long'}), 400
            
            new_password_hash = bcrypt.hashpw(data['newPassword'].encode('utf-8'), bcrypt.gensalt(rounds=12))
            
            users_collection.update_one(
                {'_id': ObjectId(request.current_user_id)},
                {'$set': {'password': new_password_hash, 'updatedAt': datetime.utcnow()}}
            )
            
            return jsonify({'message': 'Password changed successfully'}), 200
            
        except Exception as e:
            print(f"Change password error: {e}")
            return jsonify({'message': 'Internal server error'}), 500

    @auth_bp.route('/api/delete-account', methods=['DELETE'])
    @require_auth(secret_key)
    def delete_account():
        if users_collection is None or listings_collection is None:
            return jsonify({'message': 'Database connection unavailable'}), 500
        
        try:
            listings_collection.delete_many({'userId': request.current_user_id})
            
            result = users_collection.delete_one({'_id': ObjectId(request.current_user_id)})
            
            if result.deleted_count == 0:
                return jsonify({'message': 'Failed to delete account'}), 500
            
            return jsonify({'message': 'Account deleted successfully'}), 200
            
        except Exception as e:
            print(f"Delete account error: {e}")
            return jsonify({'message': 'Internal server error'}), 500

    return auth_bp
